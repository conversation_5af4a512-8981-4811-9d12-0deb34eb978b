<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Essential Property Services - Anchorage's Trusted Contractor</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.2);
      }

      /* Custom styles for HighLevel forms */
      .form-container iframe,
      .booking-container iframe {
        background-color: transparent;
      }

      /* Add custom styling for HighLevel forms */
      :root {
        --hl-primary: #4b0082; /* Your primary purple color */
        --hl-accent: #ffd700; /* Your accent gold color */
      }

      /* This will help influence the form styling through iframe */
      .form-container,
      .booking-container {
        position: relative;
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
        overflow: visible !important;
        transition: none !important;
        transform: none !important;
      }

      /* Disable any animations or hover effects */
      .form-container:hover,
      .booking-container:hover {
        transform: none !important;
        box-shadow: none !important;
      }

      /* Responsive adjustments for forms */
      @media (max-width: 768px) {
        .form-container {
          height: 800px !important; /* Adjusted height for mobile */
        }
        .consent-checkbox {
          font-size: 12px;
          margin-bottom: 0; /* Reduce bottom margin on mobile */
        }
      }

      @media (max-width: 480px) {
        .form-container {
          height: 850px !important; /* Adjusted height for small mobile */
        }
        .consent-checkbox {
          font-size: 11px;
          margin-bottom: 0; /* Reduce bottom margin on small mobile */
        }
      }

      /* Style the consent checkbox */
      .consent-checkbox {
        margin-top: 0;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .consent-checkbox input[type="checkbox"] {
        accent-color: var(--hl-primary);
        width: 16px;
        height: 16px;
      }

      /* Remove any potential padding from parent containers */
      .form-container > iframe,
      .booking-container > iframe {
        display: block;
        padding: 0;
        margin: 0;
      }

      .form-container::after,
      .booking-container::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(
          to right,
          var(--hl-primary),
          var(--hl-accent)
        );
        z-index: 10;
        pointer-events: none;
      }
    </style>

  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-6"
    >
      <div class="container mx-auto px-4 md:px-6">
        <!-- Mobile: Small floating menu on right -->
        <div class="lg:hidden flex justify-end">
          <div
            id="navContainer"
            class="backdrop-blur-sm rounded-2xl px-4 py-3 transition-all duration-300 w-auto"
          >
            <!-- Mobile Menu Button -->
            <button
              id="mobileMenuBtn"
              class="text-white"
              onclick="toggleMobileMenu()"
            >
              <svg
                class="w-7 h-7"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- Desktop: Full width navigation -->
        <div
          id="navContainerDesktop"
          class="hidden lg:block backdrop-blur-sm rounded-2xl px-6 py-4 transition-all duration-300"
        >
          <div class="flex justify-center items-center">
            <!-- Left Navigation -->
            <div class="flex items-center space-x-8 mr-auto">
              <nav>
                <ul class="flex space-x-8">
                  <li>
                    <a
                      href="services.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Services</a
                    >
                  </li>

                  <li>
                    <a
                      href="team.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Our Team</a
                    >
                  </li>
                </ul>
              </nav>
            </div>

            <!-- Right Navigation -->
            <div class="flex items-center space-x-4 ml-auto">
              <!-- Emergency Call Button -->
              <a
                href="tel:9076009900"
                class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>

              <!-- Maintenance Button -->
              <a
                href="#contact"
                class="nav-link font-bold hover:bg-white/90 transition bg-white text-primary text-sm px-4 py-3 rounded-md"
                >Maintenance</a
              >

              <!-- Estimate Button -->
              <a
                href="#scheduling"
                class="flex items-center bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Estimate
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden lg:hidden bg-white/95 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50 mt-20 max-h-[calc(100vh-5rem)] overflow-y-auto rounded-b-2xl mx-4"
      >
        <nav class="px-6 py-6">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>

            <li>
              <a
                href="team.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="#contact"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Maintenance
              </a>
            </li>
            <!-- Emergency Call Button -->
            <li class="pt-4">
              <a
                href="tel:9076009900"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>
            </li>
            <!-- Estimate Button -->
            <li>
              <a
                href="#scheduling"
                class="flex items-center w-full bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold justify-center transition mt-2"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Get Estimate
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <script>
      // Header scroll effect with transparent/white background transition
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navContainer = document.getElementById("navContainer");
        const navContainerDesktop = document.getElementById(
          "navContainerDesktop"
        );
        const navLinks = document.querySelectorAll(".nav-link");
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");

        if (window.scrollY > 20) {
          // Scrolled state - white background
          header.classList.remove("py-6");
          header.classList.add("py-4");

          // Apply to both mobile and desktop containers
          if (navContainer) {
            navContainer.classList.add("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.add("bg-white/95", "shadow-lg");
          }

          // Change text colors to dark (except maintenance button which stays white bg)
          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              // Maintenance button stays white with purple text
              link.classList.remove(
                "text-white",
                "hover:text-accent",
                "bg-white",
                "hover:bg-white/90"
              );
              link.classList.add(
                "text-primary",
                "bg-gray-100",
                "hover:bg-gray-200"
              );
            } else {
              link.classList.remove("text-white", "hover:text-accent");
              link.classList.add("text-gray-800", "hover:text-primary");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-white");
            mobileMenuBtn.classList.add("text-primary");
          }
        } else {
          // Top of page - transparent background
          header.classList.add("py-6");
          header.classList.remove("py-4");

          // Remove from both mobile and desktop containers
          if (navContainer) {
            navContainer.classList.remove("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.remove("bg-white/95", "shadow-lg");
          }

          // Change text colors to white
          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              // Maintenance button is white with purple text
              link.classList.remove(
                "text-primary",
                "bg-gray-100",
                "hover:bg-gray-200"
              );
              link.classList.add(
                "text-primary",
                "bg-white",
                "hover:bg-white/90"
              );
            } else {
              link.classList.remove("text-gray-800", "hover:text-primary");
              link.classList.add("text-white", "hover:text-accent");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-primary");
            mobileMenuBtn.classList.add("text-white");
          }
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }

      // Ensure smooth scrolling to scheduling section
      document.querySelectorAll('a[href="#scheduling"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();

          const schedulingSection = document.getElementById("scheduling");
          if (schedulingSection) {
            schedulingSection.scrollIntoView({
              behavior: "smooth",
            });
          }
        });
      });

      // Helper function to style HighLevel forms
      window.addEventListener("load", function () {
        // Add message event listener to communicate with iframes
        window.addEventListener("message", function (event) {
          // Check if the message is from HighLevel
          if (
            event.data &&
            typeof event.data === "string" &&
            event.data.includes("highLevel")
          ) {
            console.log("Received message from HighLevel form:", event.data);

            // If we receive a form height message, we could adjust container height
            if (event.data.includes("formHeight")) {
              try {
                const data = JSON.parse(event.data);
                if (data.formHeight) {
                  // Add moderate padding to ensure submit button is visible
                  const formHeight = data.formHeight + 100; // Reduced padding to minimize extra space
                  const formContainer =
                    document.querySelector(".form-container");
                  if (formContainer) {
                    formContainer.style.height = formHeight + "px";
                  }
                }
              } catch (e) {
                console.log("Error parsing form height data", e);
              }
            }
          }
        });

        // Add custom styling to form containers
        const formContainers = document.querySelectorAll(
          ".form-container, .booking-container"
        );
        formContainers.forEach((container) => {
          // Remove hover effect
          container.classList.remove("hover-grow");
          // Remove any potential padding
          container.style.padding = "0";
        });

        // Check form visibility periodically and adjust if needed
        setTimeout(function () {
          const formIframe = document.getElementById(
            "inline-wZJleRMUcriSmn86ySdF"
          );
          if (formIframe) {
            // Try to ensure the form is fully visible
            formIframe.style.minHeight = "750px";
            formIframe.style.padding = "0";
            formIframe.style.overflow = "visible";

            // Add extra space for the submit button
            const formContainer = document.querySelector(".form-container");
            if (formContainer) {
              formContainer.style.marginBottom = "0";
            }
          }
        }, 1000);

        // Handle marketing consent checkbox
        const consentCheckbox = document.getElementById("marketingConsent");
        if (consentCheckbox) {
          consentCheckbox.addEventListener("change", function () {
            // Send message to HighLevel form iframe
            const formIframe = document.getElementById(
              "inline-wZJleRMUcriSmn86ySdF"
            );
            if (formIframe && formIframe.contentWindow) {
              try {
                const message = {
                  type: "custom",
                  action: "marketingConsent",
                  value: consentCheckbox.checked,
                };
                formIframe.contentWindow.postMessage(
                  JSON.stringify(message),
                  "*"
                );

                // Store consent in localStorage for persistence
                localStorage.setItem(
                  "epsak_marketing_consent",
                  consentCheckbox.checked ? "true" : "false"
                );

                console.log(
                  "Marketing consent updated:",
                  consentCheckbox.checked
                );
              } catch (e) {
                console.error("Error sending consent to form:", e);
              }
            }
          });

          // Check if consent was previously given
          const savedConsent = localStorage.getItem("epsak_marketing_consent");
          if (savedConsent === "true") {
            consentCheckbox.checked = true;
          }
        }
      });
    </script>

    <!-- Hero Section -->
    <section class="relative min-h-[100vh] md:h-[75vh] flex items-center">
      <div class="absolute inset-0">
        <img
          src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68506368d97df57e336772da.webp"
          alt="Beautiful Alaskan Home Project"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-black opacity-60"></div>
      </div>

      <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-4xl mx-auto text-center">
          <!-- Large Logo Above Headline -->
          <div class="mb-6 md:mb-8 animate-fadeIn">
            <img
              src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681439e6dde4a49484f17fe1.webp"
              alt="Essential Property Services Logo"
              class="h-24 sm:h-32 md:h-48 lg:h-60 mx-auto brightness-0 invert"
              style="
                filter: brightness(0) invert(1)
                  drop-shadow(0 0 8px rgba(0, 0, 0, 0.5));
              "
            />
          </div>

          <h1
            class="text-4xl md:text-5xl font-bold leading-tight mb-10 animate-fadeIn text-white"
          >
            SERVING PEOPLE AND PROPERTY WITH WISDOM AND HUMILITY
          </h1>

          <!-- Three prominent CTA buttons -->
          <div class="flex flex-col md:flex-row gap-4 justify-center">
            <!-- Emergency Call Button -->
            <a
              href="tel:9076009900"
              class="px-8 py-4 bg-primary hover:bg-primary/90 text-white font-bold rounded-md text-center transition flex items-center justify-center text-lg"
            >
              <svg
                class="w-6 h-6 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              Emergency Call 24/7
            </a>

            <!-- Schedule Repair or Estimate Button -->
            <a
              href="tel:9076009900"
              class="px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-bold rounded-md text-center transition flex items-center justify-center text-lg"
            >
              <svg
                class="w-6 h-6 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              Call for Estimate
            </a>

            <!-- Schedule Service Online Button -->
            <a
              href="#scheduling"
              class="px-8 py-4 bg-white hover:bg-gray-100 text-primary font-bold rounded-md text-center transition flex items-center justify-center text-lg"
            >
              <svg
                class="w-6 h-6 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              Schedule Online
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-primary">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-accent mb-4">
            Created to Serve:
          </h2>
          <p class="text-xl text-gray-200 max-w-3xl mx-auto">
            Intentionally pursuing our calling through serving your property
            needs with excellence.
          </p>
        </div>

        <!-- Services Grid - 4 Services in a Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Emergency Services -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7d97df52dbc67c83e.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Emergency Services</h3>
                <p class="text-sm text-accent">24/7 Availability</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Round-the-clock emergency response for urgent plumbing
              and structural issues.
            </p>
          </div>

          <!-- Maintenance -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc434dcdb829c8.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Maintenance</h3>
                <p class="text-sm text-accent">Preventive Care</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Regular maintenance services to keep your property in optimal
              condition year-round.
            </p>
          </div>

          <!-- Remodeling -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7bd2be4faeaaf3744.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Remodeling</h3>
                <p class="text-sm text-accent">Complete Renovations</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Transform your spaces with comprehensive remodeling services for
              kitchens, bathrooms, and more.
            </p>
          </div>

          <!-- Cleaning -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc435f1db829c5.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Cleaning</h3>
                <p class="text-sm text-accent">Professional Cleaning</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Thorough cleaning services to maintain your property's
              appearance and hygiene standards.
            </p>
          </div>
        </div>

        <div class="mt-12 text-center">
          <a
            href="#contact"
            class="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
          >
            Request Your Free Estimate
          </a>
        </div>
      </div>
    </section>

    <!-- User Types Section -->
    <section id="user-types" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">
            Specialized Services for Your Specific Needs
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            We understand that different clients have different requirements.
            That's why we offer tailored services for each of our client types.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Property Owners -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden hover-grow">
            <div
              class="h-48 bg-cover bg-center"
              style="
                background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af76b97b2a314fdc20a.webp');
              "
            >
              <div
                class="h-full w-full bg-primary/50 flex items-center justify-center"
              >
                <h3 class="text-2xl font-bold text-white">Property Owners</h3>
              </div>
            </div>
            <div class="p-6">
              <ul class="space-y-3 mb-6">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Expert diagnosis of home issues</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Comprehensive home maintenance</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Quality remodeling and repairs</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Skilled craftsmanship you can trust</span>
                </li>
              </ul>
              <p class="text-gray-600 mb-6">
                We're passionate about diagnosing issues, maintaining homes, and
                delivering quality remodeling and repairs. Our skilled team
                enjoys the work and takes pride in every project.
              </p>
            </div>
          </div>

          <!-- Real Estate Agents -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden hover-grow">
            <div
              class="h-48 bg-cover bg-center"
              style="
                background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a253c12ce1cdb6.webp');
              "
            >
              <div
                class="h-full w-full bg-primary/50 flex items-center justify-center"
              >
                <h3 class="text-2xl font-bold text-white">
                  Real Estate Agents
                </h3>
              </div>
            </div>
            <div class="p-6">
              <ul class="space-y-3 mb-6">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Quick handling of inspection items</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Prompt response to addenda requests</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Licensed, bonded & insured work</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Guaranteed tight deadlines</span>
                </li>
              </ul>
              <p class="text-gray-600 mb-6">
                We handle inspection items and addenda quickly with licensed,
                bonded, and insured plumbing and structural work.
                Our licensed plumber on staff ensures quality
                work. We're expanding our plumbing services as part of bundled
                packages.
              </p>
            </div>
          </div>

          <!-- Property Managers -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden hover-grow">
            <div
              class="h-48 bg-cover bg-center"
              style="
                background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d59a2324f42a.webp');
              "
            >
              <div
                class="h-full w-full bg-primary/50 flex items-center justify-center"
              >
                <h3 class="text-2xl font-bold text-white">Property Managers</h3>
              </div>
            </div>
            <div class="p-6">
              <ul class="space-y-3 mb-6">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Efficient make-ready services</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Responsive tenant repair services</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Expert coordination of services</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Complete repairs from foundation to attic</span>
                </li>
              </ul>
              <p class="text-gray-600 mb-6">
                We provide make-readies, tenant repairs, and expert coordination
                for all your rental property needs. Our comprehensive services
                cover everything from foundation to attic.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Trust Section -->
    <section id="trust" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center max-w-3xl mx-auto mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            Trusted by Property Owners, Realtors & Property Managers
          </h2>
          <p class="text-gray-600 text-lg">
            Our reputation across hundreds of projects in Alaska speaks for
            itself. We're intentionally pursuing our calling through serving you
            with reliability, quality, and excellence.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <!-- Testimonial 1 -->
          <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="flex mb-4">
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </div>
            <p class="text-gray-700 italic mb-4">
              "As an Anchorage Realtor, having a contractor I can trust is SO
              important... He SHOWS UP!!"
            </p>
            <p class="font-semibold flex items-center">
              <svg
                class="w-4 h-4 mr-2 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              MJ, Key Realty Group
            </p>
          </div>

          <!-- Testimonial 2 -->
          <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="flex mb-4">
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </div>
            <p class="text-gray-700 italic mb-4">
              "Met our tight deadline for an Anchorage 4plex sale. Daily
              pictures, top-notch work. Highly recommend!"
            </p>
            <p class="font-semibold flex items-center">
              <svg
                class="w-4 h-4 mr-2 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              Gina W.
            </p>
          </div>

          <!-- Testimonial 3 -->
          <div class="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div class="flex mb-4">
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
              <svg class="w-5 h-5 text-accent fill-current" viewBox="0 0 24 24">
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </div>
            <p class="text-gray-700 italic mb-4">
              "The most ethical and honest General contractor I've met in
              Alaska. Communication was great and he kept me in the loop."
            </p>
            <p class="font-semibold flex items-center">
              <svg
                class="w-4 h-4 mr-2 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              Savanna W.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Emergency Section -->
    <section id="emergency" class="py-20 bg-primary text-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center mb-10">
          <h2 class="text-3xl md:text-5xl font-bold mb-4">
            <span class="text-accent">24/7</span> Emergency Services
          </h2>
        </div>

        <!-- Emergency Services -->
        <div class="mb-12">
          <div class="flex flex-col gap-12 items-center">
            <!-- Emergency Services Section -->
            <div
              class="bg-primary text-white rounded-xl p-8 shadow-lg w-full border border-white/20"
            >
              <div class="flex items-center mb-6">
                <div class="bg-accent rounded-full p-3 mr-4">
                  <svg
                    class="w-8 h-8 text-primary"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold">Have an Emergency?</h3>
              </div>

              <ul class="space-y-4 mb-8">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Plumbing emergencies (leaks, floods, no water)</span>
                </li>

                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Structural damage (storm, accident, fence repair)</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Heating/cooling failures in extreme weather</span>
                </li>
              </ul>

              <div class="flex flex-col sm:flex-row gap-4">
                <a
                  href="tel:9076009900"
                  class="flex items-center justify-center bg-accent text-primary hover:bg-accent/90 px-6 py-4 rounded-lg text-xl font-semibold transition transform hover:scale-105 whitespace-nowrap"
                >
                  <svg
                    class="w-6 h-6 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  Call: (*************
                </a>
                <a
                  href="#contact"
                  class="flex items-center justify-center bg-white/20 hover:bg-white/30 px-6 py-4 rounded-lg text-xl font-semibold transition"
                >
                  Request Emergency Service
                </a>
              </div>
            </div>

            <!-- Scheduling Section -->
            <div
              id="scheduling"
              class="bg-white rounded-xl shadow-xl overflow-hidden w-full"
            >
              <div
                class="p-6 bg-accent text-primary font-bold text-xl text-center"
              >
                Schedule Your Service
              </div>
              <div
                class="booking-container"
                style="height: 800px; overflow: hidden"
              >
                <iframe
                  src="https://api.leadconnectorhq.com/widget/booking/NuS5BqNCvLPpRUBBllGn"
                  style="
                    width: 100%;
                    height: 100%;
                    border: none;
                    overflow: hidden;
                  "
                  scrolling="no"
                  id="NuS5BqNCvLPpRUBBllGn_1747152639241"
                ></iframe>
                <script
                  src="https://link.msgsndr.com/js/form_embed.js"
                  type="text/javascript"
                ></script>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-5xl mx-auto">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              Let Us Serve Your Property Needs Today
            </h2>
            <p class="text-gray-600 text-lg max-w-3xl mx-auto">
              Whether you're a property owner, real estate agent, or property
              manager, we're here to serve you with excellence, humility, and
              professionalism.
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div class="bg-gray-50 rounded-lg shadow-md p-8">
              <h3 class="text-2xl font-bold mb-6 text-primary">Contact Us</h3>

              <div class="space-y-6">
                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Call Us Directly</h4>
                    <p class="text-gray-700 mb-1">(*************</p>
                    <p class="text-sm text-gray-500">
                      Fastest response for urgent needs
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Email Us</h4>
                    <p class="text-gray-700 mb-1"><EMAIL></p>
                    <p class="text-sm text-gray-500">
                      For detailed inquiries and quotes
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Hours</h4>
                    <p class="text-gray-700 mb-1">24/7/364</p>
                    <p class="text-sm text-gray-500">
                      Emergency services always available
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <h4 class="font-semibold text-lg mb-3">
                  Professional Credentials
                </h4>
                <ul class="space-y-2 text-gray-700">
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-accent mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Alaska General Contractor License 217482
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-accent mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    MOA License CON14200
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-accent mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Fully Insured and Bonded
                  </li>
                </ul>
              </div>
            </div>

            <div
              class="bg-white rounded-lg shadow-md p-4 pt-2 pb-8 border border-gray-200"
            >
              <h3 class="text-2xl font-bold mb-2 text-primary">
                Request Your Free Estimate
              </h3>
              <div
                class="form-container"
                style="height: 800px; border-radius: 6px; overflow: visible"
              >
                <iframe
                  src="https://api.leadconnectorhq.com/widget/form/wZJleRMUcriSmn86ySdF"
                  style="
                    width: 100%;
                    height: 100%;
                    border: none;
                    border-radius: 6px;
                    overflow: visible;
                  "
                  id="inline-wZJleRMUcriSmn86ySdF"
                  data-layout="{'id':'INLINE'}"
                  data-trigger-type="alwaysShow"
                  data-trigger-value=""
                  data-activation-type="alwaysActivated"
                  data-activation-value=""
                  data-deactivation-type="neverDeactivate"
                  data-deactivation-value=""
                  data-form-name="Estimate Form"
                  data-height="800"
                  data-layout-iframe-id="inline-wZJleRMUcriSmn86ySdF"
                  data-form-id="wZJleRMUcriSmn86ySdF"
                  title="Estimate Form"
                >
                </iframe>
              </div>
              <div class="consent-checkbox mt-0 mb-0 text-sm text-gray-600">
                <label class="flex items-start cursor-pointer">
                  <input
                    type="checkbox"
                    class="mt-1 mr-2"
                    id="marketingConsent"
                  />
                  <span
                    >I agree to receive marketing messaging from Essential
                    Property Services at the phone number provided above. I
                    understand I will receive 2 messages a month, data rates may
                    apply, reply STOP to opt out. This data will be mapped to
                    HighLevel.</span
                  >
                </label>
              </div>
              <script src="https://link.msgsndr.com/js/form_embed.js"></script>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681439e6dde4a49484f17fe1.webp"
              alt="Essential Property Services Logo"
              class="h-16 mb-4 brightness-0 invert"
            />
            <p class="text-gray-300 mb-4">
              Creating to serve with excellence, intentionally pursuing our
              calling through serving you with humility and professionalism.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-accent hover:text-accent/80 transition">
                <span class="sr-only">Facebook</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
              </a>
              <a href="#" class="text-accent hover:text-accent/80 transition">
                <span class="sr-only">Instagram</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:9076009900" class="hover:text-accent transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-accent transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                  ></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>Serving Anchorage, AK & Surrounding Areas</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Alaska General Contractor License 217482</li>
              <li>MOA License CON14200</li>
              <li>Fully Insured and Bonded</li>
              <li>Available 24/7/364</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Essential Property Services LLC. All rights reserved.
          </p>
          <p class="mt-2">
            <a href="privacy.html" class="hover:text-accent transition"
              >Privacy Policy</a
            >
            |
            <a href="terms.html" class="hover:text-accent transition"
              >Terms of Service</a
            >
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
